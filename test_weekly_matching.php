<?php
/**
 * Test Weekly Matching Income System
 * This script tests the new weekly matching income calculation
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/PVSystem.php';
require_once 'includes/BinaryTree.php';
require_once 'config/config.php';

try {
    $db = Database::getInstance();
    $pvSystem = new PVSystem();
    $config = Config::getInstance();
    
    echo "Testing Weekly Matching Income System...\n";
    echo str_repeat("=", 60) . "\n";
    
    // Test week dates
    $testWeekStart = '2024-01-01'; // Monday
    $testWeekEnd = '2024-01-07';   // Sunday
    
    echo "Test Week: {$testWeekStart} to {$testWeekEnd}\n";
    echo "Weekly Capping: ₹" . number_format($config->get('weekly_capping', 130000), 2) . "\n";
    echo "PV Rate: ₹" . $config->getPVRate() . " per PV\n\n";
    
    // Get users with PV data
    $stmt = $db->query("SELECT user_id FROM users WHERE status = 'active' ORDER BY user_id");
    $users = $stmt->fetchAll();
    
    echo "Users in system:\n";
    foreach ($users as $user) {
        $userPV = $pvSystem->getUserPVTotals($user['user_id']);
        $downlinePV = $pvSystem->getDownlinePVTotals($user['user_id']);
        
        $totalLeft = $userPV['left_pv'] + $downlinePV['left_pv'];
        $totalRight = $userPV['right_pv'] + $downlinePV['right_pv'];
        $matchingPV = min($totalLeft, $totalRight);
        
        echo "  {$user['user_id']}: Left={$totalLeft}, Right={$totalRight}, Matching={$matchingPV} PV\n";
    }
    echo "\n";
    
    // Test 1: Run weekly matching
    echo "Test 1: Running Weekly Matching Process\n";
    echo str_repeat("-", 40) . "\n";
    
    $result = $pvSystem->runWeeklyMatching($testWeekStart, $testWeekEnd);
    
    if ($result !== false) {
        echo "✅ Weekly matching completed successfully!\n";
        echo "Results:\n";
        echo "  - Users processed: {$result['processed']}\n";
        echo "  - Users with income: {$result['users_with_income']}\n";
        echo "  - Total income distributed: ₹" . number_format($result['total_income'], 2) . "\n";
        echo "  - Total capping applied: ₹" . number_format($result['total_capping'], 2) . "\n";
    } else {
        echo "❌ Weekly matching failed!\n";
        exit(1);
    }
    echo "\n";
    
    // Test 2: Check weekly income logs
    echo "Test 2: Checking Weekly Income Logs\n";
    echo str_repeat("-", 40) . "\n";
    
    $logStmt = $db->prepare("SELECT * FROM weekly_income_logs WHERE week_start_date = ? ORDER BY user_id");
    $logStmt->execute([$testWeekStart]);
    $logs = $logStmt->fetchAll();
    
    foreach ($logs as $log) {
        echo "User: {$log['user_id']}\n";
        echo "  Left PV: {$log['left_pv']}, Right PV: {$log['right_pv']}\n";
        echo "  Matched PV: {$log['matched_pv']}\n";
        echo "  Income: ₹" . number_format($log['income_amount'], 2) . "\n";
        echo "  Capping Applied: ₹" . number_format($log['weekly_capping_applied'], 2) . "\n";
        echo "  Carry Forward: Left={$log['carry_forward_left']}, Right={$log['carry_forward_right']}\n";
        echo "\n";
    }
    
    // Test 3: Check weekly report
    echo "Test 3: Checking Weekly Report\n";
    echo str_repeat("-", 40) . "\n";
    
    $reportStmt = $db->prepare("SELECT * FROM weekly_income_reports WHERE week_start_date = ?");
    $reportStmt->execute([$testWeekStart]);
    $report = $reportStmt->fetch();
    
    if ($report) {
        echo "Weekly Report Generated:\n";
        echo "  Week: {$report['week_start_date']} to {$report['week_end_date']}\n";
        echo "  Users Earned: {$report['total_users_earned']}\n";
        echo "  Total Income: ₹" . number_format($report['total_income_distributed'], 2) . "\n";
        echo "  Total Capping: ₹" . number_format($report['total_capping_applied'], 2) . "\n";
        echo "  Status: {$report['report_status']}\n";
        echo "  Generated: {$report['report_generated_at']}\n";
    } else {
        echo "❌ No weekly report found!\n";
    }
    echo "\n";
    
    // Test 4: Test duplicate processing prevention
    echo "Test 4: Testing Duplicate Processing Prevention\n";
    echo str_repeat("-", 40) . "\n";
    
    $result2 = $pvSystem->runWeeklyMatching($testWeekStart, $testWeekEnd);
    
    if ($result2 !== false && $result2['users_with_income'] == 0) {
        echo "✅ Duplicate processing prevention working correctly!\n";
        echo "Second run processed {$result2['processed']} users but distributed no income (already processed)\n";
    } else {
        echo "❌ Duplicate processing prevention not working!\n";
    }
    echo "\n";
    
    // Test 5: Test weekly capping
    echo "Test 5: Testing Weekly Capping\n";
    echo str_repeat("-", 40) . "\n";
    
    $weeklyCapping = $config->get('weekly_capping', 130000);
    $cappingTestFound = false;
    
    foreach ($logs as $log) {
        if ($log['weekly_capping_applied'] > 0) {
            echo "✅ Weekly capping applied to user {$log['user_id']}\n";
            echo "  Original income would have been: ₹" . number_format($log['income_amount'] + $log['weekly_capping_applied'], 2) . "\n";
            echo "  Capped to: ₹" . number_format($log['income_amount'], 2) . "\n";
            echo "  Capping applied: ₹" . number_format($log['weekly_capping_applied'], 2) . "\n";
            $cappingTestFound = true;
        }
    }
    
    if (!$cappingTestFound) {
        echo "ℹ️  No users exceeded weekly capping limit of ₹" . number_format($weeklyCapping, 2) . "\n";
        echo "This is normal if all users' matching income is below the cap.\n";
    }
    echo "\n";
    
    echo "✅ All tests completed successfully!\n";
    echo "Weekly matching income system is working correctly.\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
