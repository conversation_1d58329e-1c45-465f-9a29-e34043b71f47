<?php
/**
 * Test Email Notification System
 * This script tests the weekly income report email functionality
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/PVSystem.php';
require_once 'config/config.php';
require_once 'includes/EmailService.php';

try {
    echo "Testing Email Notification System...\n";
    echo str_repeat("=", 50) . "\n";
    
    $emailService = new EmailService();
    
    // Test data
    $testWeekStart = '2024-01-15';
    $testWeekEnd = '2024-01-21';
    $testReportData = [
        'processed' => 5,
        'users_with_income' => 3,
        'total_income' => 1250.75,
        'total_capping' => 125.50
    ];
    
    echo "Test Parameters:\n";
    echo "- Week: {$testWeekStart} to {$testWeekEnd}\n";
    echo "- Users processed: {$testReportData['processed']}\n";
    echo "- Users with income: {$testReportData['users_with_income']}\n";
    echo "- Total income: ₹" . number_format($testReportData['total_income'], 2) . "\n";
    echo "- Total capping: ₹" . number_format($testReportData['total_capping'], 2) . "\n\n";
    
    echo "Sending test email notification...\n";
    
    $result = $emailService->sendWeeklyIncomeReport($testWeekStart, $testWeekEnd, $testReportData);
    
    if ($result) {
        echo "✅ Email notification sent successfully!\n";
        echo "Check the logs/email_log.txt file to see the email content.\n";
    } else {
        echo "❌ Failed to send email notification.\n";
    }
    
    // Check if log file was created
    if (file_exists('logs/email_log.txt')) {
        echo "\n📧 Email log file created successfully.\n";
        echo "Log file location: logs/email_log.txt\n";
        
        // Show last few lines of the log
        $logContent = file_get_contents('logs/email_log.txt');
        $lines = explode("\n", $logContent);
        $lastLines = array_slice($lines, -10);
        
        echo "\nLast 10 lines of email log:\n";
        echo str_repeat("-", 30) . "\n";
        foreach ($lastLines as $line) {
            echo $line . "\n";
        }
    } else {
        echo "\n❌ Email log file was not created.\n";
    }
    
    echo "\n✅ Email notification test completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
