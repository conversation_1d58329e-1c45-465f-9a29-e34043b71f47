<?php
/**
 * Test Weekly Income Capping
 * This script tests the weekly income capping functionality
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/PVSystem.php';
require_once 'includes/BinaryTree.php';
require_once 'config/config.php';

try {
    $db = Database::getInstance();
    $pvSystem = new PVSystem();
    $config = Config::getInstance();
    
    echo "Testing Weekly Income Capping...\n";
    echo str_repeat("=", 50) . "\n";
    
    $weeklyCapping = $config->get('weekly_capping', 130000);
    $pvRate = $config->getPVRate();
    
    echo "Weekly Capping Limit: ₹" . number_format($weeklyCapping, 2) . "\n";
    echo "PV Rate: ₹{$pvRate} per PV\n";
    
    // Calculate PV needed to exceed weekly cap
    $pvNeededToExceedCap = ($weeklyCapping / $pvRate) + 10000; // Add 10000 PV to exceed cap significantly
    echo "PV needed to exceed cap: " . number_format($pvNeededToExceedCap, 0) . " PV\n";
    echo "This would generate: ₹" . number_format($pvNeededToExceedCap * $pvRate, 2) . " income\n\n";
    
    // Create a test scenario with high PV
    $testUserId = 'SP20240001';
    $testWeekStart = '2024-01-08'; // Different week to avoid conflicts
    $testWeekEnd = '2024-01-14';
    
    echo "Test Scenario:\n";
    echo "- User: {$testUserId}\n";
    echo "- Week: {$testWeekStart} to {$testWeekEnd}\n";
    echo "- Adding high PV amounts to test capping\n\n";
    
    // Clear any existing data for this test week
    $db->prepare("DELETE FROM weekly_income_logs WHERE week_start_date = ?")->execute([$testWeekStart]);
    $db->prepare("DELETE FROM weekly_income_reports WHERE week_start_date = ?")->execute([$testWeekStart]);
    
    // Add high PV amounts to create a scenario where capping is needed
    echo "Step 1: Adding high PV amounts to test user...\n";
    
    // Add large amounts to both left and right sides to ensure high matching
    $largePVAmount = $pvNeededToExceedCap / 2; // Split between left and right
    
    // Add to left side (simulate downline activity)
    $pvSystem->addDirectPV($testUserId, $largePVAmount, 'left', 'test_bonus', null, 'TEST_CAPPING_LEFT', 'Test high left PV for capping');
    echo "Added {$largePVAmount} PV to left side\n";
    
    // Add to right side (simulate downline activity)
    $pvSystem->addDirectPV($testUserId, $largePVAmount, 'right', 'test_bonus', null, 'TEST_CAPPING_RIGHT', 'Test high right PV for capping');
    echo "Added {$largePVAmount} PV to right side\n";
    
    // Check current PV totals
    $currentPV = $pvSystem->getUserPVTotals($testUserId);
    $downlinePV = $pvSystem->getDownlinePVTotals($testUserId);
    
    $totalLeft = $currentPV['left_pv'] + $downlinePV['left_pv'];
    $totalRight = $currentPV['right_pv'] + $downlinePV['right_pv'];
    $matchingPV = min($totalLeft, $totalRight);
    $expectedIncome = $matchingPV * $pvRate;
    
    echo "\nCurrent PV Status:\n";
    echo "- Total Left PV: " . number_format($totalLeft, 2) . "\n";
    echo "- Total Right PV: " . number_format($totalRight, 2) . "\n";
    echo "- Matching PV: " . number_format($matchingPV, 2) . "\n";
    echo "- Expected Income (before capping): ₹" . number_format($expectedIncome, 2) . "\n";
    
    if ($expectedIncome > $weeklyCapping) {
        echo "✅ Income exceeds weekly cap - capping should be applied\n";
    } else {
        echo "❌ Income does not exceed weekly cap - test scenario insufficient\n";
        exit(1);
    }
    echo "\n";
    
    // Step 2: Run weekly matching
    echo "Step 2: Running weekly matching to test capping...\n";
    
    $result = $pvSystem->runWeeklyMatching($testWeekStart, $testWeekEnd);
    
    if ($result !== false) {
        echo "✅ Weekly matching completed\n";
        echo "Results:\n";
        echo "- Users processed: {$result['processed']}\n";
        echo "- Users with income: {$result['users_with_income']}\n";
        echo "- Total income distributed: ₹" . number_format($result['total_income'], 2) . "\n";
        echo "- Total capping applied: ₹" . number_format($result['total_capping'], 2) . "\n";
    } else {
        echo "❌ Weekly matching failed\n";
        exit(1);
    }
    echo "\n";
    
    // Step 3: Verify capping was applied
    echo "Step 3: Verifying weekly capping was applied...\n";
    
    $logStmt = $db->prepare("SELECT * FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
    $logStmt->execute([$testUserId, $testWeekStart]);
    $log = $logStmt->fetch();
    
    if ($log) {
        echo "Weekly Income Log for {$testUserId}:\n";
        echo "- Left PV: " . number_format($log['left_pv'], 2) . "\n";
        echo "- Right PV: " . number_format($log['right_pv'], 2) . "\n";
        echo "- Matched PV: " . number_format($log['matched_pv'], 2) . "\n";
        echo "- Income Amount: ₹" . number_format($log['income_amount'], 2) . "\n";
        echo "- Weekly Capping Applied: ₹" . number_format($log['weekly_capping_applied'], 2) . "\n";
        echo "- Carry Forward Left: " . number_format($log['carry_forward_left'], 2) . "\n";
        echo "- Carry Forward Right: " . number_format($log['carry_forward_right'], 2) . "\n";
        
        // Verify capping logic
        $originalIncome = $log['income_amount'] + $log['weekly_capping_applied'];
        echo "\nCapping Verification:\n";
        echo "- Original calculated income: ₹" . number_format($originalIncome, 2) . "\n";
        echo "- Weekly capping limit: ₹" . number_format($weeklyCapping, 2) . "\n";
        echo "- Final income after capping: ₹" . number_format($log['income_amount'], 2) . "\n";
        
        if ($log['weekly_capping_applied'] > 0) {
            echo "✅ Weekly capping was applied correctly!\n";
            echo "- Amount capped: ₹" . number_format($log['weekly_capping_applied'], 2) . "\n";
            
            if ($log['income_amount'] == $weeklyCapping) {
                echo "✅ Final income equals weekly capping limit\n";
            } else {
                echo "❌ Final income does not equal weekly capping limit\n";
            }
        } else {
            echo "❌ No weekly capping was applied (unexpected)\n";
        }
    } else {
        echo "❌ No weekly income log found for test user\n";
    }
    echo "\n";
    
    // Cleanup test data
    echo "Cleaning up test data...\n";
    $db->prepare("DELETE FROM pv_transactions WHERE reference_id LIKE 'TEST_CAPPING_%'")->execute();
    $db->prepare("DELETE FROM weekly_income_logs WHERE week_start_date = ?")->execute([$testWeekStart]);
    $db->prepare("DELETE FROM weekly_income_reports WHERE week_start_date = ?")->execute([$testWeekStart]);
    echo "✅ Test data cleaned up\n\n";
    
    echo "✅ Weekly income capping test completed successfully!\n";
    echo "The system correctly applies the ₹" . number_format($weeklyCapping, 2) . " weekly income cap.\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
