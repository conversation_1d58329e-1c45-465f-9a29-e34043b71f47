<?php
/**
 * Weekly Income Reports - Admin Panel
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../config/config.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Initialize classes
$pvSystem = new PVSystem();
$config = Config::getInstance();

// Get database instance
$db = Database::getInstance();

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';
$messageType = '';

if ($action === 'generate_report') {
    $weekStart = $_POST['week_start'] ?? '';
    $weekEnd = $_POST['week_end'] ?? '';
    
    if ($weekStart && $weekEnd) {
        $result = $pvSystem->runWeeklyMatching($weekStart, $weekEnd);
        if ($result !== false) {
            $message = "Weekly report generated successfully! Processed {$result['processed']} users, distributed ₹" . number_format($result['total_income'], 2);
            $messageType = 'success';
        } else {
            $message = "Failed to generate weekly report.";
            $messageType = 'danger';
        }
    }
}

// Get weekly reports
$reportsStmt = $db->query("
    SELECT * FROM weekly_income_reports 
    ORDER BY week_start_date DESC 
    LIMIT 20
");
$reports = $reportsStmt->fetchAll();

// Get current week dates
$currentWeekStart = date('Y-m-d', strtotime('monday this week'));
$currentWeekEnd = date('Y-m-d', strtotime('sunday this week'));
$lastWeekStart = date('Y-m-d', strtotime('monday last week'));
$lastWeekEnd = date('Y-m-d', strtotime('sunday last week'));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Income Reports - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/admin_navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/admin_sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-chart-line me-2"></i>Weekly Income Reports</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#generateReportModal">
                            <i class="fas fa-plus me-1"></i>Generate Report
                        </button>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Total Reports</h6>
                                        <h3><?php echo count($reports); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Weekly Cap</h6>
                                        <h3>₹<?php echo number_format($config->get('weekly_capping', 130000), 0); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-hand-holding-usd fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">PV Rate</h6>
                                        <h3>₹<?php echo $config->getPVRate(); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-coins fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Processing Day</h6>
                                        <h3><?php 
                                            $days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                                            echo $days[$config->get('weekly_processing_day', 0)]; 
                                        ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Weekly Income Reports</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($reports)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Reports Generated Yet</h5>
                                <p class="text-muted">Generate your first weekly income report using the button above.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Week Period</th>
                                            <th>Users Earned</th>
                                            <th>Total Income</th>
                                            <th>Capping Applied</th>
                                            <th>Status</th>
                                            <th>Generated</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reports as $report): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo date('M d', strtotime($report['week_start_date'])); ?> - <?php echo date('M d, Y', strtotime($report['week_end_date'])); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $report['total_users_earned']; ?> users</span>
                                                </td>
                                                <td>
                                                    <strong class="text-success">₹<?php echo number_format($report['total_income_distributed'], 2); ?></strong>
                                                </td>
                                                <td>
                                                    <?php if ($report['total_capping_applied'] > 0): ?>
                                                        <span class="text-warning">₹<?php echo number_format($report['total_capping_applied'], 2); ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">₹0.00</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = [
                                                        'generated' => 'bg-warning',
                                                        'sent' => 'bg-success',
                                                        'failed' => 'bg-danger'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $statusClass[$report['report_status']] ?? 'bg-secondary'; ?>">
                                                        <?php echo ucfirst($report['report_status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo date('M d, Y H:i', strtotime($report['report_generated_at'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <a href="weekly-income-details.php?week=<?php echo $report['week_start_date']; ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> View Details
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Generate Report Modal -->
    <div class="modal fade" id="generateReportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="?action=generate_report">
                    <div class="modal-header">
                        <h5 class="modal-title">Generate Weekly Income Report</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="week_start" class="form-label">Week Start Date</label>
                                <input type="date" class="form-control" id="week_start" name="week_start" 
                                       value="<?php echo $lastWeekStart; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="week_end" class="form-label">Week End Date</label>
                                <input type="date" class="form-control" id="week_end" name="week_end" 
                                       value="<?php echo $lastWeekEnd; ?>" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Generate income report for the specified week. This will process PV matching for all users.
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Generate Report</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
